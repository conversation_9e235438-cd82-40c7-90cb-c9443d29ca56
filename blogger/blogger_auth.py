import requests
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
import json

# Blogger API OAuth 2.0 Configuration
CLIENT_ID = '************-3ueqt1buqfbffcrfrjv4slinhpths2im.apps.googleusercontent.com'
CLIENT_SECRET = '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'
REDIRECT_URI = 'https://api.flowkar.com/api/blogger-auth/'
SCOPES = ['https://www.googleapis.com/auth/blogger']

def generate_blogger_auth_url(user_id):
    """Generate Blogger OAuth 2.0 authorization URL"""
    auth_url = (
        f"https://accounts.google.com/o/oauth2/auth?"
        f"client_id={CLIENT_ID}&"
        f"redirect_uri={REDIRECT_URI}&"
        f"scope={' '.join(SCOPES).replace(' ', '%20')}&"
        f"response_type=code&"
        f"access_type=offline&"
        f"prompt=consent&"
        f"state={user_id}"
    )
    return auth_url

def blogger_code_to_token(code):
    """Exchange authorization code for access and refresh tokens"""
    try:
        url = "https://oauth2.googleapis.com/token"
        data = {
            'client_id': CLIENT_ID,
            'client_secret': CLIENT_SECRET,
            'code': code,
            'grant_type': 'authorization_code',
            'redirect_uri': REDIRECT_URI
        }
        
        response = requests.post(url, data=data)
        
        if response.status_code == 200:
            token_data = response.json()
            return {
                'access_token': token_data.get('access_token'),
                'refresh_token': token_data.get('refresh_token'),
                'expires_in': token_data.get('expires_in'),
                'token_type': token_data.get('token_type')
            }
        else:
            return {'error': response.json()}
    except Exception as e:
        return {'error': str(e)}

def refresh_blogger_token(refresh_token):
    """Refresh the access token using refresh token"""
    try:
        url = "https://oauth2.googleapis.com/token"
        data = {
            'client_id': CLIENT_ID,
            'client_secret': CLIENT_SECRET,
            'refresh_token': refresh_token,
            'grant_type': 'refresh_token'
        }
        
        response = requests.post(url, data=data)
        
        if response.status_code == 200:
            token_data = response.json()
            return {
                'access_token': token_data.get('access_token'),
                'expires_in': token_data.get('expires_in'),
                'token_type': token_data.get('token_type')
            }
        else:
            return {'error': response.json()}
    except Exception as e:
        return {'error': str(e)}

def get_blogger_user_info(access_token):
    """Get user's Blogger profile information"""
    try:
        url = "https://www.googleapis.com/blogger/v3/users/self"
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            user_data = response.json()
            return {
                'user_id': user_data.get('id'),
                'display_name': user_data.get('displayName'),
                'url': user_data.get('url'),
                'about': user_data.get('about'),
                'locale': user_data.get('locale')
            }
        else:
            return {'error': response.json()}
    except Exception as e:
        return {'error': str(e)}

def get_user_blogs(access_token):
    """Get list of user's blogs"""
    try:
        url = "https://www.googleapis.com/blogger/v3/users/self/blogs"
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            blogs_data = response.json()
            blogs = []
            
            if 'items' in blogs_data:
                for blog in blogs_data['items']:
                    blogs.append({
                        'blog_id': blog.get('id'),
                        'name': blog.get('name'),
                        'description': blog.get('description'),
                        'url': blog.get('url'),
                        'status': blog.get('status'),
                        'posts_count': blog.get('posts', {}).get('totalItems', 0)
                    })
            
            return {'blogs': blogs}
        else:
            return {'error': response.json()}
    except Exception as e:
        return {'error': str(e)}

def validate_blogger_token(access_token):
    """Validate if the access token is still valid"""
    try:
        url = "https://www.googleapis.com/blogger/v3/users/self"
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.get(url, headers=headers)
        return response.status_code == 200
    except Exception as e:
        return False
